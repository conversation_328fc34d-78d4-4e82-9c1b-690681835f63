// 主要JavaScript功能

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 自动隐藏警告消息
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // 搜索功能增强
    initializeSearch();

    // 文件上传功能
    initializeFileUpload();

    // 表单验证
    initializeFormValidation();

    // 初始化Google Analytics事件跟踪
    initializeAnalyticsTracking();

    // 启动性能和用户参与度跟踪
    trackPagePerformance();
    trackUserEngagement();
});

// Google Analytics事件跟踪
function initializeAnalyticsTracking() {
    // 检查gtag是否可用
    if (typeof gtag === 'undefined') {
        return;
    }

    // 跟踪外部链接点击
    $('a[href^="http"]').not('[href*="' + window.location.hostname + '"]').on('click', function(e) {
        var url = $(this).attr('href');
        gtag('event', 'click', {
            event_category: 'outbound',
            event_label: url,
            transport_type: 'beacon'
        });
    });

    // 跟踪文件下载
    $('a[href$=".pdf"], a[href$=".doc"], a[href$=".docx"], a[href$=".zip"], a[href$=".md"]').on('click', function(e) {
        var filename = $(this).attr('href').split('/').pop();
        gtag('event', 'file_download', {
            event_category: 'engagement',
            event_label: filename
        });
    });

    // 跟踪搜索行为
    $('form[action*="search"], input[name="search"]').on('submit', function(e) {
        var searchTerm = $(this).find('input[name="search"]').val() || $(this).val();
        if (searchTerm) {
            gtag('event', 'search', {
                search_term: searchTerm,
                event_category: 'engagement'
            });
        }
    });

    // 跟踪页面滚动深度
    var scrollDepthTracked = false;
    $(window).on('scroll', function() {
        var scrollPercent = Math.round(($(window).scrollTop() / ($(document).height() - $(window).height())) * 100);

        if (scrollPercent >= 75 && !scrollDepthTracked) {
            gtag('event', 'scroll', {
                event_category: 'engagement',
                event_label: '75_percent',
                value: 75
            });
            scrollDepthTracked = true;
        }
    });

    // 跟踪按钮点击
    $('button, .btn').on('click', function(e) {
        var buttonText = $(this).text().trim() || $(this).attr('title') || $(this).attr('aria-label') || 'unknown';
        var buttonClass = $(this).attr('class') || '';

        gtag('event', 'button_click', {
            event_category: 'engagement',
            event_label: buttonText,
            custom_parameter_1: buttonClass
        });
    });
}

// Google Analytics工具函数
function trackAnalyticsEvent(action, category, label, value, customParams) {
    if (typeof gtag === 'undefined') {
        return;
    }

    var eventData = {
        event_category: category || 'general',
        event_label: label || ''
    };

    if (value !== undefined) {
        eventData.value = value;
    }

    // 添加自定义参数
    if (customParams && typeof customParams === 'object') {
        Object.assign(eventData, customParams);
    }

    gtag('event', action, eventData);
}

// 跟踪页面性能
function trackPagePerformance() {
    if (typeof gtag === 'undefined' || !window.performance) {
        return;
    }

    window.addEventListener('load', function() {
        setTimeout(function() {
            var perfData = window.performance.timing;
            var loadTime = perfData.loadEventEnd - perfData.navigationStart;
            var domReadyTime = perfData.domContentLoadedEventEnd - perfData.navigationStart;

            // 跟踪页面加载时间
            gtag('event', 'timing_complete', {
                name: 'load',
                value: loadTime
            });

            // 跟踪DOM准备时间
            gtag('event', 'timing_complete', {
                name: 'dom_ready',
                value: domReadyTime
            });
        }, 1000);
    });
}

// 跟踪用户参与度
function trackUserEngagement() {
    if (typeof gtag === 'undefined') {
        return;
    }

    var startTime = Date.now();
    var engaged = false;

    // 跟踪用户活跃时间
    function trackEngagement() {
        if (!engaged) {
            engaged = true;
            gtag('event', 'user_engagement', {
                engagement_time_msec: Date.now() - startTime
            });
        }
    }

    // 监听用户交互
    document.addEventListener('click', trackEngagement);
    document.addEventListener('scroll', trackEngagement);
    document.addEventListener('keydown', trackEngagement);

    // 页面离开时跟踪停留时间
    window.addEventListener('beforeunload', function() {
        var timeOnPage = Date.now() - startTime;
        gtag('event', 'page_view_time', {
            event_category: 'engagement',
            value: Math.round(timeOnPage / 1000) // 转换为秒
        });
    });
}

// 搜索功能
function initializeSearch() {
    const searchInput = $('input[name="search"]');
    const searchForm = searchInput.closest('form');
    let searchTimeout;

    // 实时搜索（可选）
    searchInput.on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                performSearch(query);
            }, 500);
        }
    });

    // 清除搜索
    $('.search-clear').on('click', function(e) {
        e.preventDefault();
        searchInput.val('');
        searchForm.submit();
    });
}

// 执行搜索
function performSearch(query) {
    // 这里可以实现AJAX搜索
    console.log('Searching for:', query);
}

// 文件上传功能
function initializeFileUpload() {
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        const fileInput = $(this).find('input[type="file"]');
        
        if (files.length > 0) {
            fileInput[0].files = files;
            handleFileSelection(fileInput[0]);
        }
    });

    $('input[type="file"]').on('change', function() {
        handleFileSelection(this);
    });
}

// 处理文件选择
function handleFileSelection(input) {
    const file = input.files[0];
    if (!file) return;

    const maxSize = 16 * 1024 * 1024; // 16MB
    if (file.size > maxSize) {
        alert('文件大小不能超过16MB');
        input.value = '';
        return;
    }

    // 显示文件信息
    const fileInfo = $(input).siblings('.file-info');
    if (fileInfo.length) {
        fileInfo.html(`
            <div class="alert alert-info">
                <i class="fas fa-file me-2"></i>
                已选择文件: ${file.name} (${formatFileSize(file.size)})
            </div>
        `);
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 表单验证
function initializeFormValidation() {
    // 邮箱验证
    $('input[type="email"]').on('blur', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
            showFieldError($(this), '请输入有效的邮箱地址');
        } else {
            $(this).removeClass('is-invalid');
            hideFieldError($(this));
        }
    });

    // URL验证
    $('input[type="url"]').on('blur', function() {
        const url = $(this).val();
        const urlRegex = /^https?:\/\/.+/;
        
        if (url && !urlRegex.test(url)) {
            $(this).addClass('is-invalid');
            showFieldError($(this), '请输入有效的URL（以http://或https://开头）');
        } else {
            $(this).removeClass('is-invalid');
            hideFieldError($(this));
        }
    });

    // 必填字段验证
    $('input[required], textarea[required], select[required]').on('blur', function() {
        if (!$(this).val().trim()) {
            $(this).addClass('is-invalid');
            showFieldError($(this), '此字段为必填项');
        } else {
            $(this).removeClass('is-invalid');
            hideFieldError($(this));
        }
    });
}

// 显示字段错误
function showFieldError(field, message) {
    hideFieldError(field);
    field.after(`<div class="invalid-feedback">${message}</div>`);
}

// 隐藏字段错误
function hideFieldError(field) {
    field.siblings('.invalid-feedback').remove();
}

// AJAX请求辅助函数
function makeAjaxRequest(url, method, data, successCallback, errorCallback) {
    const csrfToken = $('meta[name="csrf-token"]').attr('content');
    
    $.ajax({
        url: url,
        method: method,
        data: data,
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (successCallback) successCallback(response);
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            if (errorCallback) {
                errorCallback(xhr, status, error);
            } else {
                alert('请求失败，请稍后重试');
            }
        }
    });
}

// 显示加载状态
function showLoading(element) {
    const originalText = element.html();
    element.data('original-text', originalText);
    element.html('<span class="loading"></span> 处理中...');
    element.prop('disabled', true);
}

// 隐藏加载状态
function hideLoading(element) {
    const originalText = element.data('original-text');
    element.html(originalText);
    element.prop('disabled', false);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('已复制到剪贴板', 'success');
    }).catch(function(err) {
        console.error('复制失败:', err);
        showToast('复制失败', 'error');
    });
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // 创建toast容器（如果不存在）
    if (!$('.toast-container').length) {
        $('body').append('<div class="toast-container position-fixed bottom-0 end-0 p-3"></div>');
    }
    
    const $toast = $(toastHtml);
    $('.toast-container').append($toast);
    
    const toast = new bootstrap.Toast($toast[0]);
    toast.show();
    
    // 自动移除
    $toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
