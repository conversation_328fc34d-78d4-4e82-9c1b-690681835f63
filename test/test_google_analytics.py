#!/usr/bin/env python3
"""
Google Analytics集成测试
测试GA4跟踪代码是否正确集成到页面中
"""

import os
import sys
import re
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_ga_integration_in_template():
    """测试模板中是否包含Google Analytics代码"""
    print("🔍 测试模板中的Google Analytics集成...")
    
    template_path = os.path.join(project_root, 'templates', 'base.html')
    
    if not os.path.exists(template_path):
        print("❌ 基础模板文件不存在")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 检查GA4跟踪代码
    checks = [
        ('googletagmanager.com/gtag/js', 'Google Tag Manager脚本'),
        ('gtag(\'js\', new Date())', 'gtag初始化'),
        ('gtag(\'config\'', 'GA配置'),
        ('config.GOOGLE_ANALYTICS_ID', '配置变量引用'),
        ('trackEvent', '自定义事件跟踪函数'),
        ('custom_dimension_1', '自定义维度'),
    ]
    
    all_passed = True
    for check, description in checks:
        if check in template_content:
            print(f"  ✅ {description}: 已集成")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def test_config_variables():
    """测试配置文件中的GA变量"""
    print("\n⚙️ 测试配置文件中的Google Analytics变量...")
    
    # 测试主配置文件
    app_init_path = os.path.join(project_root, 'app', '__init__.py')
    production_config_path = os.path.join(project_root, 'config', 'production.py')
    env_example_path = os.path.join(project_root, '.env.example')
    
    files_to_check = [
        (app_init_path, 'app/__init__.py'),
        (production_config_path, 'config/production.py'),
        (env_example_path, '.env.example')
    ]
    
    all_passed = True
    for file_path, file_name in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'GOOGLE_ANALYTICS_ID' in content:
                print(f"  ✅ {file_name}: 包含GA配置")
            else:
                print(f"  ❌ {file_name}: 缺少GA配置")
                all_passed = False
        else:
            print(f"  ⚠️  {file_name}: 文件不存在")
    
    return all_passed

def test_javascript_integration():
    """测试JavaScript文件中的GA集成"""
    print("\n📜 测试JavaScript中的Google Analytics集成...")
    
    js_path = os.path.join(project_root, 'static', 'js', 'main.js')
    
    if not os.path.exists(js_path):
        print("❌ main.js文件不存在")
        return False
    
    with open(js_path, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    # 检查JavaScript GA功能
    js_checks = [
        ('initializeAnalyticsTracking', '分析跟踪初始化'),
        ('trackAnalyticsEvent', '事件跟踪函数'),
        ('trackPagePerformance', '性能跟踪'),
        ('trackUserEngagement', '用户参与度跟踪'),
        ('gtag(\'event\'', '事件发送'),
        ('event_category', '事件分类'),
        ('file_download', '文件下载跟踪'),
        ('search_term', '搜索跟踪'),
        ('scroll', '滚动跟踪'),
        ('button_click', '按钮点击跟踪'),
    ]
    
    all_passed = True
    for check, description in js_checks:
        if check in js_content:
            print(f"  ✅ {description}: 已实现")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def test_app_with_ga_config():
    """测试应用在有GA配置时的行为"""
    print("\n🚀 测试应用在有Google Analytics配置时的行为...")
    
    # 模拟有GA ID的环境
    test_ga_id = 'G-TEST123456'
    
    with patch.dict(os.environ, {'GOOGLE_ANALYTICS_ID': test_ga_id}):
        try:
            from app import create_app
            app = create_app()
            
            with app.test_client() as client:
                # 测试首页
                response = client.get('/')
                
                if response.status_code == 200:
                    html_content = response.get_data(as_text=True)
                    
                    # 检查GA代码是否在响应中
                    if test_ga_id in html_content:
                        print(f"  ✅ GA ID ({test_ga_id}) 出现在页面中")
                        return True
                    else:
                        print(f"  ❌ GA ID ({test_ga_id}) 未出现在页面中")
                        return False
                else:
                    print(f"  ❌ 页面请求失败，状态码: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"  ❌ 应用测试失败: {e}")
            return False

def test_app_without_ga_config():
    """测试应用在没有GA配置时的行为"""
    print("\n🔒 测试应用在没有Google Analytics配置时的行为...")
    
    # 确保没有GA ID环境变量
    env_without_ga = {k: v for k, v in os.environ.items() if k != 'GOOGLE_ANALYTICS_ID'}
    
    with patch.dict(os.environ, env_without_ga, clear=True):
        try:
            from app import create_app
            app = create_app()
            
            with app.test_client() as client:
                response = client.get('/')
                
                if response.status_code == 200:
                    html_content = response.get_data(as_text=True)
                    
                    # 检查GA代码是否不在响应中
                    if 'googletagmanager.com' not in html_content:
                        print("  ✅ 没有GA配置时，GA代码不会加载")
                        return True
                    else:
                        print("  ❌ 没有GA配置时，GA代码仍然加载")
                        return False
                else:
                    print(f"  ❌ 页面请求失败，状态码: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"  ❌ 应用测试失败: {e}")
            return False

def run_all_tests():
    """运行所有Google Analytics集成测试"""
    print("🔬 Google Analytics 集成测试")
    print("=" * 50)
    
    tests = [
        test_ga_integration_in_template,
        test_config_variables,
        test_javascript_integration,
        test_app_with_ga_config,
        test_app_without_ga_config,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Google Analytics集成测试通过！")
        print("\n📋 下一步操作:")
        print("1. 在 .env 文件中设置 GOOGLE_ANALYTICS_ID")
        print("2. 重启应用")
        print("3. 访问网站并在GA实时报告中验证数据")
        return True
    else:
        print("⚠️  部分测试失败，请检查集成配置")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
