{% extends "base.html" %}

{% block title %}Web3项目深度分析报告 - 专业区块链项目研究与DeFi项目评估平台{% endblock %}

{% block description %}专业的Web3项目深度分析报告平台，提供最新的区块链项目研究、DeFi项目评估、加密货币项目分析。获取权威的Web3投资建议和技术分析报告，助您把握区块链投资机会。{% endblock %}

{% block keywords %}Web3项目分析,区块链项目报告,DeFi项目评估,加密货币项目研究,Web3投资分析,区块链技术分析,数字货币项目,去中心化金融,NFT项目分析,智能合约审计{% endblock %}

{% block og_type %}website{% endblock %}

{% block structured_data %}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Web3项目深度分析报告",
    "description": "专业的Web3项目深度分析报告平台，提供最新的区块链项目研究、DeFi项目评估、加密货币项目分析",
    "url": "{{ request.url }}",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ url_for('public.index', _external=True) }}?search={search_term_string}",
        "query-input": "required name=search_term_string"
    },
    "publisher": {
        "@type": "Organization",
        "name": "Web3项目深度分析报告平台",
        "description": "专注于Web3和区块链项目研究的专业平台"
    },
    "mainEntity": {
        "@type": "ItemList",
        "name": "Web3项目分析报告列表",
        "description": "最新的Web3项目深度分析报告",
        "numberOfItems": {{ reports|length if reports else 0 }},
        "itemListElement": [
            {% for report in reports %}
            {
                "@type": "ListItem",
                "position": {{ loop.index }},
                "item": {
                    "@type": "Report",
                    "name": "{{ report.project_name }}",
                    "description": "{{ report.project_name }}的深度分析报告",
                    "url": "{{ url_for('public.view_report', report_id=report.id, _external=True) }}",
                    "datePublished": "{{ report.created_at | datetime('%Y-%m-%dT%H:%M:%S') if report.created_at else '' }}",
                    "author": {
                        "@type": "Organization",
                        "name": "Web3项目深度分析报告平台"
                    }
                }
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.98) 25%,
        rgba(241, 245, 249, 0.98) 50%,
        rgba(236, 242, 248, 0.98) 75%,
        rgba(230, 238, 247, 0.98) 100%);
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(148, 163, 184, 0.2);
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    position: relative;
    overflow: hidden;
    color: #334155 !important;
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 4px 12px rgba(0, 0, 0, 0.03);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.04) 0%, transparent 50%);
    opacity: 0.7;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.8" fill="rgba(51,65,85,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.6;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    color: #1e293b !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    font-weight: 700;
}

.hero-section .lead {
    color: #475569 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.6);
}

.bg-gradient-primary {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.98) 25%,
        rgba(241, 245, 249, 0.98) 50%,
        rgba(236, 242, 248, 0.98) 75%,
        rgba(230, 238, 247, 0.98) 100%) !important;
}

/* Hero Section Enhanced Elements */
.hero-section .badge-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    color: #475569;
    border: 1px solid rgba(148, 163, 184, 0.2);
    backdrop-filter: blur(10px);
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    font-weight: 500;
    transition: all 0.3s ease;
}

.hero-section .badge-premium:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
    color: #334155;
    transform: translateY(-1px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.hero-section .btn-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    color: #334155;
    border: 1px solid rgba(148, 163, 184, 0.3);
    backdrop-filter: blur(10px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hero-section .btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.hero-section .btn-premium:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(241, 245, 249, 0.95) 100%);
    color: #1e293b;
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-2px);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.hero-section .btn-premium:hover::before {
    left: 100%;
}

.hero-section .btn-premium:active {
    transform: translateY(-1px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Search Section Styles */
.search-section .search-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    backdrop-filter: blur(10px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.search-section .search-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(241, 245, 249, 0.95) 100%);
    border-color: rgba(148, 163, 184, 0.3);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.search-section h3 {
    color: #334155 !important;
    font-weight: 600;
}

.search-section .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.3);
    color: #334155;
    border-radius: 0.5rem 0 0 0.5rem;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.search-section .form-control:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
    color: #1e293b;
}

.search-section .form-control::placeholder {
    color: #64748b;
}

.search-section .btn-primary {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%);
    border: 1px solid rgba(99, 102, 241, 0.8);
    color: white;
    border-radius: 0 0.5rem 0.5rem 0;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    font-weight: 500;
}

.search-section .btn-primary:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 1) 0%, rgba(79, 70, 229, 1) 100%);
    border-color: rgba(99, 102, 241, 1);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.search-section .input-group-lg .form-control {
    border-radius: 0.5rem 0 0 0.5rem;
}

.search-section .input-group-lg .btn {
    border-radius: 0 0.5rem 0.5rem 0;
}

/* Table Styles Enhancement */
.table {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table thead th {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
    color: #334155;
    font-weight: 600;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    padding: 1rem 0.75rem;
}

.table tbody tr {
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.table tbody td {
    color: #475569;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table .btn {
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.table .btn-outline-primary {
    color: #6366f1;
    border-color: rgba(99, 102, 241, 0.3);
    background: rgba(99, 102, 241, 0.05);
}

.table .btn-outline-primary:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.5);
    color: #4f46e5;
    transform: translateY(-1px);
}

.table .btn-outline-success {
    color: #059669;
    border-color: rgba(5, 150, 105, 0.3);
    background: rgba(5, 150, 105, 0.05);
}

.table .btn-outline-success:hover {
    background: rgba(5, 150, 105, 0.1);
    border-color: rgba(5, 150, 105, 0.5);
    color: #047857;
    transform: translateY(-1px);
}

.table .btn-outline-secondary {
    color: #64748b;
    border-color: rgba(100, 116, 139, 0.3);
    background: rgba(100, 116, 139, 0.05);
}

.table .btn-outline-secondary:hover {
    background: rgba(100, 116, 139, 0.1);
    border-color: rgba(100, 116, 139, 0.5);
    color: #475569;
    transform: translateY(-1px);
}

/* Card Enhancement */
.card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    backdrop-filter: blur(10px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(241, 245, 249, 0.95) 100%);
    border-color: rgba(148, 163, 184, 0.3);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Report Cards Enhancement */
.report-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(10px);
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(241, 245, 249, 0.95) 100%);
}

.report-card .card-title {
    color: #2c3e50;
    font-weight: 600;
}

.report-card .badge {
    font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 !important;
    }

    .hero-section .display-5 {
        font-size: 2rem;
    }

    .search-section .input-group-lg .form-control,
    .search-section .input-group-lg .btn {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
}

/* SEO优化的语义化样式 */
main[role="main"] {
    min-height: 60vh;
}

section {
    scroll-margin-top: 80px; /* 为锚点导航预留空间 */
}

/* Modal and Form Enhancement */
.modal-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    backdrop-filter: blur(15px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.modal-header {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    color: #334155;
}

.modal-title {
    color: #1e293b;
    font-weight: 600;
}

.modal-body .form-label {
    color: #334155;
    font-weight: 500;
}

.modal-body .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.3);
    color: #334155;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.modal-body .form-control:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
    color: #1e293b;
}

.modal-body .form-control::placeholder {
    color: #64748b;
}

.modal-body .form-text {
    color: #64748b;
}

.modal-footer {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
    border-top: 1px solid rgba(148, 163, 184, 0.2);
}

.modal-footer .btn-secondary {
    background: linear-gradient(135deg, rgba(100, 116, 139, 0.9) 0%, rgba(71, 85, 105, 0.9) 100%);
    border: 1px solid rgba(100, 116, 139, 0.8);
    color: white;
    backdrop-filter: blur(5px);
}

.modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, rgba(100, 116, 139, 1) 0%, rgba(71, 85, 105, 1) 100%);
    transform: translateY(-1px);
}

.modal-footer .btn-primary {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%);
    border: 1px solid rgba(99, 102, 241, 0.8);
    color: white;
    backdrop-filter: blur(5px);
}

.modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 1) 0%, rgba(79, 70, 229, 1) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* 可访问性增强 */
.btn:focus,
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

/* 加载状态样式 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
</style>
{% endblock %}

{% block content %}
<!-- SEO优化的页面结构 -->
<main role="main">
    <!-- Hero Section -->
    <section class="hero-section bg-gradient-primary py-5 mb-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-chart-line me-3"></i>Web3项目深度分析报告
                    </h1>
                    <p class="lead mb-4">
                        专业的区块链项目研究平台，提供权威的Web3项目分析、DeFi项目评估和加密货币投资建议。
                        助您把握区块链投资机会，洞察Web3发展趋势。
                    </p>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge badge-premium px-3 py-2">区块链项目分析</span>
                        <span class="badge badge-premium px-3 py-2">DeFi项目评估</span>
                        <span class="badge badge-premium px-3 py-2">Web3投资研究</span>
                        <span class="badge badge-premium px-3 py-2">智能合约审计</span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <button type="button" class="btn btn-premium btn-lg px-4 py-3" data-bs-toggle="modal" data-bs-target="#requestModal">
                        <i class="fas fa-plus me-2"></i>申请项目分析
                    </button>
                </div>
            </div>
        </div>
    </section>

<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <header class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-2">
                        <i class="fas fa-list me-2"></i>最新Web3项目分析报告
                    </h2>
                    <p class="text-muted mb-0">浏览我们最新发布的区块链项目深度研究报告</p>
                </div>
            </header>

            <!-- Search Section -->
            <section class="search-section mb-5">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="search-card bg-light p-4 rounded-3">
                            <h3 class="h5 mb-3 text-center">
                                <i class="fas fa-search me-2"></i>搜索Web3项目分析报告
                            </h3>
                            <form method="GET" action="{{ url_for('public.index') }}" role="search">
                                <div class="input-group input-group-lg">
                                    <input type="search" class="form-control" name="search"
                                           placeholder="搜索项目名称、区块链类型、DeFi协议..."
                                           value="{{ search_query or '' }}"
                                           aria-label="搜索Web3项目">
                                    <button class="btn btn-primary" type="submit" aria-label="执行搜索">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    {% if search_query %}
                                    <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary" aria-label="清除搜索">
                                        <i class="fas fa-times"></i> 清除
                                    </a>
                                    {% endif %}
                                </div>
                                <div class="mt-2 text-center">
                                    <small class="text-muted">
                                        热门搜索：
                                        <a href="{{ url_for('public.index') }}?search=DeFi" class="text-decoration-none me-2">DeFi</a>
                                        <a href="{{ url_for('public.index') }}?search=NFT" class="text-decoration-none me-2">NFT</a>
                                        <a href="{{ url_for('public.index') }}?search=Layer2" class="text-decoration-none me-2">Layer2</a>
                                        <a href="{{ url_for('public.index') }}?search=GameFi" class="text-decoration-none">GameFi</a>
                                    </small>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>

        <!-- Reports List -->
        {% if reports %}
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>项目名称</th>
                                <th>创建时间</th>
                                <th>最后更新</th>
                                <th class="text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        <div>
                                            <strong>{{ report.project_name }}</strong>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    <small class="text-muted">
                                        {{ report.created_at[:10] if report.created_at else '未知' }}
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ report.updated_at[:10] if report.updated_at else '未知' }}
                                    </small>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('public.view_analysis', report_id=report.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="查看分析页面">
                                            <i class="fas fa-chart-bar"></i> 分析
                                        </a>
                                        <a href="{{ url_for('public.view_report', report_id=report.id) }}" 
                                           class="btn btn-sm btn-outline-success" title="查看研究报告">
                                            <i class="fas fa-file-text"></i> 报告
                                        </a>
                                        {% if report.official_website %}
                                        <a href="{{ report.official_website }}" target="_blank" 
                                           class="btn btn-sm btn-outline-info" title="访问官方网站">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {% if pagination and pagination.total_pages > 1 %}
        <nav aria-label="报告分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in range(1, pagination.total_pages + 1) %}
                    {% if page_num == pagination.page %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('public.index', page=page_num, search=search_query) }}">{{ page_num }}</a>
                    </li>
                    {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <!-- No Reports Found -->
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h3 class="text-muted">
                {% if search_query %}
                    没有找到匹配的项目
                {% else %}
                    暂无研究报告
                {% endif %}
            </h3>
            <p class="text-muted">
                {% if search_query %}
                    尝试使用不同的关键词搜索，或者
                {% endif %}
                <a href="#" data-bs-toggle="modal" data-bs-target="#requestModal" class="text-decoration-none">
                    申请添加新项目
                </a>
            </p>
        </div>
        {% endif %}

        <!-- User Requests Section -->
        <div class="mt-5">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="h4">
                    <i class="fas fa-users me-2"></i>请求列表
                    {% if requests_total > 0 %}
                        <span class="badge bg-secondary ms-2">{{ requests_total }}</span>
                    {% endif %}
                </h3>
            </div>

            {% if requests %}
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>项目名称</th>
                                    <th>用户邮箱</th>
                                    <th>官方网站</th>
                                    <th>状态</th>
                                    <th>申请时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for req in requests %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-project-diagram text-info me-2"></i>
                                            <strong>{{ req.project_name }}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ req.user_email }}</span>
                                    </td>
                                    <td>
                                        {% if req.official_website %}
                                        <a href="{{ req.official_website }}" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-external-link-alt me-1"></i>访问
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if req.status == 'pending' %}
                                            <span class="badge bg-warning">待处理</span>
                                        {% elif req.status == 'approved' %}
                                            <span class="badge bg-success">已批准</span>
                                        {% elif req.status == 'completed' %}
                                            <span class="badge bg-primary">已完成</span>
                                        {% elif req.status == 'rejected' %}
                                            <span class="badge bg-danger">已拒绝</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ req.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ req.created_at | datetime('%Y-%m-%d') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- No Requests Found -->
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                <h5 class="text-muted">
                    {% if search_query %}
                        没有找到匹配的用户请求
                    {% endif %}
                </h5>
            </div>
            {% endif %}
        </div>

        <!-- Can't find project link -->
        <div class="text-center mt-4">
            <p class="text-muted">
                找不到您想要的项目？
                <a href="#" data-bs-toggle="modal" data-bs-target="#requestModal" class="text-decoration-none">
                    <i class="fas fa-plus-circle me-1"></i>申请研究新项目
                </a>
            </p>
        </div>
    </div>
</div>

<!-- Request Project Modal -->
<div class="modal fade" id="requestModal" tabindex="-1" aria-labelledby="requestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="requestModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>申请项目研究
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    请提交您希望研究的项目。由于计算资源限制，报告生成可能会有延迟。完成后我们会通过邮件通知您，谢谢！
                </div>
                
                <form id="requestForm">
                    <div class="mb-3">
                        <label for="userEmail" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="userEmail" name="email" required>
                        <div class="form-text">我们将通过此邮箱通知您报告完成情况</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="projectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="projectName" name="project_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="officialWebsite" class="form-label">官方网站 <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="officialWebsite" name="official_website" 
                               placeholder="https://example.com" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitRequest">
                    <i class="fas fa-paper-plane me-1"></i>提交申请
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 项目申请表单处理
document.getElementById('submitRequest').addEventListener('click', function() {
    const form = document.getElementById('requestForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 基本验证
    if (!data.email || !data.project_name || !data.official_website) {
        alert('请填写所有必填字段');
        return;
    }
    
    // 提交请求
    fetch('{{ url_for("public.request_project") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            bootstrap.Modal.getInstance(document.getElementById('requestModal')).hide();
            form.reset();
        } else {
            alert('错误：' + (result.errors ? result.errors.join('\n') : '提交失败'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('提交时出现错误，请稍后重试');
    });
});
</script>
{% endblock %}
