{% extends "admin/base.html" %}

{% block title %}用户请求管理 - 管理后台{% endblock %}
{% block page_title %}用户请求管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">
        <i class="fas fa-inbox me-2"></i>用户请求列表
    </h4>
    <div class="btn-group" role="group">
        <input type="radio" class="btn-check" name="statusFilter" id="filter-all" value="all" 
               {% if status_filter == 'all' %}checked{% endif %}>
        <label class="btn btn-outline-primary" for="filter-all">全部</label>
        
        <input type="radio" class="btn-check" name="statusFilter" id="filter-pending" value="pending"
               {% if status_filter == 'pending' %}checked{% endif %}>
        <label class="btn btn-outline-warning" for="filter-pending">待处理</label>
        
        <input type="radio" class="btn-check" name="statusFilter" id="filter-processing" value="processing"
               {% if status_filter == 'processing' %}checked{% endif %}>
        <label class="btn btn-outline-info" for="filter-processing">处理中</label>
        
        <input type="radio" class="btn-check" name="statusFilter" id="filter-completed" value="completed"
               {% if status_filter == 'completed' %}checked{% endif %}>
        <label class="btn btn-outline-success" for="filter-completed">已完成</label>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ pagination.total or 0 }}</div>
                    <div class="stat-label">请求总数</div>
                </div>
                <div class="text-primary">
                    <i class="fas fa-inbox fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 请求列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>请求列表
            {% if status_filter != 'all' %}
                <span class="badge bg-secondary ms-2">{{ status_filter }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        {% if requests %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>项目信息</th>
                            <th>用户信息</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for req in requests %}
                        <tr>
                            <td>
                                <div class="fw-bold">{{ req.project_name }}</div>
                                {% if req.project_url %}
                                <small class="text-muted">
                                    <a href="{{ req.project_url }}" target="_blank" class="text-decoration-none">
                                        <i class="fas fa-external-link-alt me-1"></i>{{ req.project_url[:50] }}{% if req.project_url|length > 50 %}...{% endif %}
                                    </a>
                                </small>
                                {% endif %}
                                {% if req.description %}
                                <div class="small text-muted mt-1">{{ req.description[:100] }}{% if req.description|length > 100 %}...{% endif %}</div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="fw-bold">{{ req.user_name }}</div>
                                <small class="text-muted">{{ req.user_email }}</small>
                                {% if req.user_company %}
                                <div class="small text-muted">{{ req.user_company }}</div>
                                {% endif %}
                            </td>
                            <td>
                                {% if req.status == 'pending' %}
                                    <span class="badge bg-warning">待处理</span>
                                {% elif req.status == 'processing' %}
                                    <span class="badge bg-info">处理中</span>
                                {% elif req.status == 'completed' %}
                                    <span class="badge bg-success">已完成</span>
                                {% elif req.status == 'rejected' %}
                                    <span class="badge bg-danger">已拒绝</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ req.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ req.created_at | datetime }}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary"
                                            onclick="viewRequest('{{ req.id }}')" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if req.status != 'completed' and req.status != 'rejected' %}
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="updateStatus('{{ req.id }}', 'processing')" title="标记为处理中">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning"
                                            onclick="updateStatus('{{ req.id }}', 'completed')" title="标记为已完成">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="updateStatus('{{ req.id }}', 'rejected')" title="拒绝">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="deleteRequest('{{ req.id }}')" title="删除请求">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.total_pages > 1 %}
            <nav aria-label="请求分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.requests', page=pagination.prev_num, status=status_filter) }}">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.page %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.requests', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                        </li>
                        {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.requests', page=pagination.next_num, status=status_filter) }}">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无请求</h5>
                <p class="text-muted">
                    {% if status_filter == 'all' %}
                        还没有用户提交研究请求
                    {% else %}
                        当前筛选条件下没有请求
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 请求详情模态框 -->
<div class="modal fade" id="requestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">请求详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="requestModalBody">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 完成请求上传文件对话框 - 使用原生dialog -->
<dialog id="completeRequestDialog" style="
    width: 90%;
    max-width: 800px;
    padding: 0;
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
">
    <div style="padding: 1.5rem;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; border-bottom: 1px solid #dee2e6; padding-bottom: 1rem;">
            <h5 style="margin: 0; color: #495057; font-weight: 600;">
                <i class="fas fa-upload me-2"></i>完成请求 - 上传报告文件
            </h5>
            <button type="button" id="closeDialogBtn" style="
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #6c757d;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">&times;</button>
        </div>

        <form id="completeRequestForm" enctype="multipart/form-data">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                请上传项目的研究报告文件和分析HTML文件，完成后将自动创建报告并标记请求为已完成。
            </div>

            <div id="requestInfoSection">
                <!-- 请求信息将通过JavaScript动态填充 -->
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reportFile" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>报告文件 <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control" id="reportFile" name="report_file"
                               accept=".md" required>
                        <div class="form-text">请上传Markdown格式的报告文件 (.md)</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="analysisFile" class="form-label">
                            <i class="fas fa-chart-bar me-1"></i>分析文件 <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control" id="analysisFile" name="analysis_file"
                               accept=".html,.htm" required>
                        <div class="form-text">请上传HTML格式的分析文件 (.html)</div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="creatorName" class="form-label">
                    <i class="fas fa-user me-1"></i>创建者名称 <span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control" id="creatorName" name="creator_name"
                       value="{{ current_user.name if current_user.name else current_user.email }}" required>
            </div>

            <div class="mb-3">
                <label for="reportDescription" class="form-label">
                    <i class="fas fa-align-left me-1"></i>报告描述
                </label>
                <textarea class="form-control" id="reportDescription" name="description" rows="3"
                          placeholder="请简要描述这个报告的内容和特点..."></textarea>
            </div>

            <div class="mb-3">
                <label for="adminNotes" class="form-label">
                    <i class="fas fa-sticky-note me-1"></i>管理员备注
                </label>
                <textarea class="form-control" id="adminNotes" name="admin_notes" rows="2"
                          placeholder="可选：添加处理备注..."></textarea>
            </div>

            <div style="display: flex; justify-content: flex-end; gap: 0.5rem; margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #dee2e6;">
                <button type="button" class="btn btn-secondary" id="cancelDialogBtn">取消</button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check me-1"></i>完成请求并创建报告
                </button>
            </div>
        </form>
    </div>
</dialog>

<script>
// 状态筛选
document.querySelectorAll('input[name="statusFilter"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const status = this.value;
        window.location.href = `{{ url_for('admin.requests') }}?status=${status}`;
    });
});

function viewRequest(requestId) {
    // 显示请求详情模态框
    const modal = new bootstrap.Modal(document.getElementById('requestModal'));
    const modalBody = document.getElementById('requestModalBody');

    // 显示加载状态
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载请求详情...</p>
        </div>
    `;

    // 显示模态框
    modal.show();

    // 获取请求详情
    fetch(`/admin/requests/${requestId}`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCSRFToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const request = data.data;
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">基本信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>项目名称:</strong></td>
                                <td>${request.project_name || '未知'}</td>
                            </tr>
                            <tr>
                                <td><strong>用户邮箱:</strong></td>
                                <td>${request.user_email || '未知'}</td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td>
                                    <span class="badge ${getStatusBadgeClass(request.status)}">
                                        ${request.status_display || request.status}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间:</strong></td>
                                <td>${request.created_at_formatted || request.created_at || '未知'}</td>
                            </tr>
                            ${request.processed_at_formatted ? `
                            <tr>
                                <td><strong>处理时间:</strong></td>
                                <td>${request.processed_at_formatted}</td>
                            </tr>
                            ` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">详细信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>请求ID:</strong></td>
                                <td><code>${request.id}</code></td>
                            </tr>
                            ${request.processed_by ? `
                            <tr>
                                <td><strong>处理人:</strong></td>
                                <td>${request.processed_by}</td>
                            </tr>
                            ` : ''}
                            ${request.admin_notes ? `
                            <tr>
                                <td><strong>管理员备注:</strong></td>
                                <td>${request.admin_notes}</td>
                            </tr>
                            ` : ''}
                        </table>
                    </div>
                </div>
                ${request.description ? `
                <div class="mt-3">
                    <h6 class="text-muted">请求描述</h6>
                    <div class="border rounded p-3 bg-light">
                        ${request.description.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
            `;
        } else {
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    加载请求详情失败: ${data.error || '未知错误'}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('获取请求详情失败:', error);
        modalBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                网络错误，请稍后重试
            </div>
        `;
    });
}

function getStatusBadgeClass(status) {
    switch(status) {
        case 'pending': return 'bg-warning';
        case 'processing': return 'bg-info';
        case 'completed': return 'bg-success';
        case 'rejected': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function updateStatus(requestId, newStatus) {
    let confirmMessage = '';
    let actionName = '';

    switch(newStatus) {
        case 'processing':
            confirmMessage = '确定要将此请求标记为处理中吗？';
            actionName = '标记为处理中';
            break;
        case 'completed':
            // 对于完成状态，显示上传文件模态框
            showCompleteRequestModal(requestId);
            return;
        case 'rejected':
            confirmMessage = '确定要拒绝此请求吗？\n\n注意：拒绝的请求将从数据库中删除，无法恢复。';
            actionName = '拒绝请求';
            break;
        default:
            confirmMessage = '确定要更新此请求的状态吗？';
            actionName = '更新状态';
    }

    if (confirm(confirmMessage)) {
        // 显示加载状态
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // 发送状态更新请求
        fetch(`/admin/requests/${requestId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (newStatus === 'rejected') {
                    // 拒绝请求成功，从页面移除该行
                    const row = button.closest('tr');
                    row.style.transition = 'opacity 0.3s ease';
                    row.style.opacity = '0';
                    setTimeout(() => {
                        row.remove();
                        // 检查是否还有请求，如果没有则显示空状态
                        const tbody = document.querySelector('tbody');
                        if (tbody.children.length === 0) {
                            tbody.innerHTML = `
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无请求数据</p>
                                    </td>
                                </tr>
                            `;
                        }
                    }, 300);

                    // 显示成功消息
                    showAlert('success', '请求已成功拒绝并删除');
                } else {
                    // 其他状态更新成功，刷新页面
                    location.reload();
                }
            } else {
                alert(actionName + '失败: ' + (data.message || '未知错误'));
                button.disabled = false;
                button.innerHTML = originalContent;
            }
        })
        .catch(error => {
            console.error(actionName + '请求失败:', error);
            alert(actionName + '请求失败，请稍后重试');
            button.disabled = false;
            button.innerHTML = originalContent;
        });
    }
}

function deleteRequest(requestId) {
    const confirmMessage = '确定要删除此请求吗？\n\n注意：删除操作无法恢复，请谨慎操作。';

    if (confirm(confirmMessage)) {
        // 显示加载状态
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // 发送删除请求
        fetch(`/admin/requests/${requestId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，从页面移除该行
                const row = button.closest('tr');
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    // 检查是否还有请求，如果没有则显示空状态
                    const tbody = document.querySelector('tbody');
                    if (tbody.children.length === 0) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无请求数据</p>
                                </td>
                            </tr>
                        `;
                    }
                }, 300);

                // 显示成功消息
                showAlert('success', '请求已成功删除');
            } else {
                alert('删除失败: ' + (data.message || '未知错误'));
                button.disabled = false;
                button.innerHTML = originalContent;
            }
        })
        .catch(error => {
            console.error('删除请求失败:', error);
            alert('删除请求失败，请稍后重试');
            button.disabled = false;
            button.innerHTML = originalContent;
        });
    }
}

function showCompleteRequestModal(requestId) {
    // 获取请求详情
    fetch(`/admin/requests/${requestId}`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCSRFToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const request = data.data;

            // 填充请求信息
            const requestInfoSection = document.getElementById('requestInfoSection');
            requestInfoSection.innerHTML = `
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <h6 class="card-title">请求信息</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>项目名称:</strong> ${request.project_name}</p>
                                <p class="mb-1"><strong>用户邮箱:</strong> ${request.user_email}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>官方网站:</strong> <a href="${request.official_website}" target="_blank">${request.official_website}</a></p>
                                <p class="mb-1"><strong>创建时间:</strong> ${request.created_at_formatted}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 存储请求ID用于后续提交
            document.getElementById('completeRequestForm').dataset.requestId = requestId;

            // 显示原生对话框
            const dialog = document.getElementById('completeRequestDialog');
            console.log('📢 显示完成请求对话框');
            dialog.showModal();
        } else {
            alert('获取请求详情失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('获取请求详情失败:', error);
        alert('获取请求详情失败，请稍后重试');
    });
}

function getCSRFToken() {
    // 从meta标签获取CSRF token
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}

function showAlert(type, message) {
    // 创建警告消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 原生对话框事件处理
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 初始化原生对话框...');

    const dialog = document.getElementById('completeRequestDialog');
    const closeBtn = document.getElementById('closeDialogBtn');
    const cancelBtn = document.getElementById('cancelDialogBtn');

    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            console.log('🔘 关闭对话框');
            dialog.close();
        });
    }

    // 取消按钮事件
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            console.log('🔘 取消对话框');
            dialog.close();
        });
    }

    // 点击对话框外部关闭
    if (dialog) {
        dialog.addEventListener('click', function(e) {
            if (e.target === dialog) {
                console.log('🔘 点击外部关闭对话框');
                dialog.close();
            }
        });
    }

    console.log('✅ 对话框初始化完成');
});

// 处理完成请求表单提交
document.getElementById('completeRequestForm').addEventListener('submit', function(e) {
    e.preventDefault();

    console.log('表单提交事件触发'); // 调试日志

    const requestId = this.dataset.requestId;
    const formData = new FormData(this);

    // 验证文件
    const reportFile = document.getElementById('reportFile').files[0];
    const analysisFile = document.getElementById('analysisFile').files[0];

    if (!reportFile || !analysisFile) {
        alert('请上传报告文件和分析文件');
        return;
    }

    // 显示提交中状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';

    // 提交表单
    fetch(`/admin/requests/${requestId}/complete`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCSRFToken()
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('请求已成功完成并创建报告！');
            document.getElementById('completeRequestDialog').close();
            location.reload();
        } else {
            alert('完成请求失败: ' + (data.error || '未知错误'));
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('完成请求失败:', error);
        alert('完成请求失败，请稍后重试');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 文件上传验证
document.getElementById('reportFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (!file.name.toLowerCase().endsWith('.md')) {
            alert('报告文件必须是Markdown格式 (.md)');
            e.target.value = '';
        } else if (file.size > 5 * 1024 * 1024) {
            alert('报告文件大小不能超过5MB');
            e.target.value = '';
        }
    }
});

document.getElementById('analysisFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const validExtensions = ['.html', '.htm'];
        const isValid = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));

        if (!isValid) {
            alert('分析文件必须是HTML格式 (.html 或 .htm)');
            e.target.value = '';
        } else if (file.size > 10 * 1024 * 1024) {
            alert('分析文件大小不能超过10MB');
            e.target.value = '';
        }
    }
});
</script>
{% endblock %}
