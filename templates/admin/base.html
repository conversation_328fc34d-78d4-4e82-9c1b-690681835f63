<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}管理后台 - 项目研究报告平台{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        .admin-sidebar {
            background: rgba(248, 249, 250, 0.98);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            min-height: 100vh;
            color: #495057;
        }

        .admin-sidebar .nav-link {
            color: #6c757d;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 0.25rem;
            transition: all 0.3s ease;
        }

        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: #495057;
            background-color: rgba(108, 117, 125, 0.1);
        }

        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.5rem;
        }

        .admin-sidebar h4 {
            color: #495057;
        }

        .admin-sidebar .opacity-75 {
            color: #6c757d !important;
        }

        .admin-content {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            min-height: 100vh;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        /* 精确修复模态框交互问题 - 只针对模态框 */

        /* 只修复模态框，不影响其他元素 */
        #completeRequestModal {
            z-index: 1060 !important;
            pointer-events: auto !important;
        }

        #completeRequestModal .modal-backdrop {
            z-index: 1055 !important;
        }

        #completeRequestModal .modal-dialog {
            z-index: 1061 !important;
            pointer-events: auto !important;
        }

        #completeRequestModal .modal-content {
            z-index: 1062 !important;
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(15px) !important;
            border: 1px solid rgba(148, 163, 184, 0.3) !important;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
            pointer-events: auto !important;
        }

        /* 只修复模态框内的表单元素 */
        #completeRequestModal input,
        #completeRequestModal textarea,
        #completeRequestModal select,
        #completeRequestModal button,
        #completeRequestModal .btn,
        #completeRequestModal .form-control {
            pointer-events: auto !important;
            cursor: pointer !important;
            position: relative !important;
            z-index: 1063 !important;
        }

        /* 特别处理文件输入 */
        #completeRequestModal input[type="file"] {
            cursor: pointer !important;
            pointer-events: auto !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .stat-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-left: 4px solid #0d6efd;
            margin-bottom: 1.5rem;
        }
        
        .stat-card.success {
            border-left-color: #198754;
        }
        
        .stat-card.warning {
            border-left-color: #ffc107;
        }
        
        .stat-card.danger {
            border-left-color: #dc3545;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #495057;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                min-height: auto;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>管理后台
                        </h4>
                        <small class="opacity-75">{{ current_user.name if current_user.is_authenticated else '管理员' }}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" 
                           href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>仪表板
                        </a>
                        <a class="nav-link {% if request.endpoint == 'admin.reports' %}active{% endif %}" 
                           href="{{ url_for('admin.reports') }}">
                            <i class="fas fa-file-alt"></i>报告管理
                        </a>
                        <a class="nav-link {% if request.endpoint == 'admin.requests' %}active{% endif %}" 
                           href="{{ url_for('admin.requests') }}">
                            <i class="fas fa-inbox"></i>用户请求
                        </a>
                        
                        <hr class="my-3" style="border-color: rgba(255, 255, 255, 0.2);">
                        
                        <a class="nav-link" href="{{ url_for('public.index') }}" target="_blank">
                            <i class="fas fa-external-link-alt"></i>查看网站
                        </a>
                        <a class="nav-link" href="{{ url_for('admin.logout') }}">
                            <i class="fas fa-sign-out-alt"></i>退出登录
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-content p-3">
                    <!-- Header -->
                    <div class="admin-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h3 mb-0">{% block page_title %}管理后台{% endblock %}</h1>
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3">
                                    <i class="fas fa-user me-1"></i>{{ current_user.name if current_user.is_authenticated else '管理员' }}
                                </span>
                                <a href="{{ url_for('admin.logout') }}" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-sign-out-alt me-1"></i>退出
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <!-- Page Content -->
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <script>
        // 自动隐藏警告消息
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
