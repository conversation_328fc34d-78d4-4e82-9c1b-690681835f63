# Google Analytics 集成说明

## 📊 概述

本项目已成功集成Google Analytics 4 (GA4)，提供全面的网站分析和用户行为跟踪功能。

## 🚀 功能特性

### 1. 基础跟踪
- **页面浏览量**: 自动跟踪所有页面访问
- **用户会话**: 跟踪用户会话时长和页面停留时间
- **设备信息**: 自动收集设备、浏览器、操作系统信息
- **地理位置**: 跟踪用户地理位置（国家/地区）

### 2. 自定义事件跟踪
- **搜索行为**: 跟踪用户搜索关键词和搜索频率
- **文件下载**: 跟踪PDF、文档等文件下载
- **外部链接**: 跟踪用户点击外部链接的行为
- **按钮交互**: 跟踪所有按钮和链接的点击事件
- **滚动深度**: 跟踪用户页面滚动深度（75%阈值）

### 3. 性能监控
- **页面加载时间**: 监控页面完整加载时间
- **DOM准备时间**: 监控DOM内容加载完成时间
- **用户参与度**: 跟踪用户活跃交互时间

### 4. 电子商务跟踪（可扩展）
- 为未来的付费功能预留跟踪接口
- 支持转化目标设置

## ⚙️ 配置步骤

### 1. 获取Google Analytics ID

1. 访问 [Google Analytics](https://analytics.google.com/)
2. 创建新的GA4属性
3. 获取测量ID（格式：G-XXXXXXXXXX）

### 2. 配置环境变量

在 `.env` 文件中添加：
```env
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
```

### 3. 验证集成

1. 启动应用
2. 访问网站页面
3. 在Google Analytics实时报告中查看数据

## 📈 跟踪的事件类型

### 自动事件
- `page_view`: 页面浏览
- `session_start`: 会话开始
- `first_visit`: 首次访问

### 自定义事件
- `search`: 搜索行为
  - `search_term`: 搜索关键词
- `file_download`: 文件下载
  - `event_label`: 文件名
- `button_click`: 按钮点击
  - `event_label`: 按钮文本
- `scroll`: 滚动深度
  - `event_label`: 滚动百分比
- `user_engagement`: 用户参与
  - `engagement_time_msec`: 参与时间（毫秒）
- `timing_complete`: 性能计时
  - `name`: 计时类型（load/dom_ready）
  - `value`: 时间值（毫秒）

## 🔧 自定义跟踪

### 在JavaScript中手动跟踪事件

```javascript
// 基础事件跟踪
trackAnalyticsEvent('custom_action', 'category', 'label', value);

// 带自定义参数的事件跟踪
trackAnalyticsEvent('form_submit', 'engagement', 'contact_form', null, {
    form_type: 'contact',
    user_type: 'visitor'
});

// 直接使用gtag（如果可用）
if (typeof gtag !== 'undefined') {
    gtag('event', 'custom_event', {
        event_category: 'custom',
        event_label: 'test',
        value: 1
    });
}
```

### 在Python后端跟踪服务器端事件

```python
# 可以通过Measurement Protocol API发送服务器端事件
# 适用于表单提交、用户注册等服务器端操作
```

## 📊 推荐的GA4配置

### 1. 转化目标设置
- 用户请求提交
- 报告下载完成
- 分析页面深度浏览

### 2. 自定义维度
- 页面类型（首页、报告页、分析页等）
- 用户类型（新用户、回访用户）
- 内容类别（DeFi、NFT、Layer2等）

### 3. 受众群体
- 活跃用户（多次访问）
- 深度用户（长时间停留）
- 转化用户（完成目标操作）

## 🔒 隐私和合规

### GDPR合规
- 用户可以选择退出跟踪
- 不收集个人身份信息
- 遵循数据最小化原则

### Cookie政策
- 使用Google Analytics cookies
- 建议添加Cookie同意横幅
- 提供隐私政策链接

## 🚨 故障排除

### 常见问题

1. **数据不显示**
   - 检查GOOGLE_ANALYTICS_ID环境变量
   - 确认GA4属性配置正确
   - 查看浏览器控制台错误

2. **事件不触发**
   - 检查JavaScript控制台错误
   - 确认gtag函数可用
   - 验证事件参数格式

3. **实时数据延迟**
   - GA4实时报告可能有1-2分钟延迟
   - 标准报告有24-48小时延迟

### 调试模式

在开发环境中启用调试：
```javascript
// 在浏览器控制台中启用GA调试
gtag('config', 'GA_MEASUREMENT_ID', {
    debug_mode: true
});
```

## 📚 相关资源

- [Google Analytics 4 文档](https://developers.google.com/analytics/devguides/collection/ga4)
- [gtag.js 参考](https://developers.google.com/analytics/devguides/collection/gtagjs)
- [事件参考](https://developers.google.com/analytics/devguides/collection/ga4/events)
- [Measurement Protocol](https://developers.google.com/analytics/devguides/collection/protocol/ga4)

## 🔄 更新日志

- **v1.0.0**: 初始集成GA4基础跟踪
- **v1.1.0**: 添加自定义事件跟踪
- **v1.2.0**: 增加性能监控和用户参与度跟踪

## 📞 支持

如有问题或需要自定义跟踪需求，请联系开发团队。
