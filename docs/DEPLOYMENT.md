# 🚀 Supabase部署完整指南

## 第一步：Supabase项目设置

### 1.1 创建Supabase项目
1. 访问 https://supabase.com 并注册/登录
2. 点击 "New Project"
3. 填写项目信息：
   - **Name**: `defix-research-platform`
   - **Database Password**: 设置强密码
   - **Region**: 选择最近的区域
4. 等待项目创建完成（1-2分钟）

### 1.2 获取项目配置
在项目仪表板中：
1. 进入 Settings → API
2. 记录以下信息：
   - **Project URL**: `https://your-project-id.supabase.co`
   - **anon public key**: `eyJ...`
   - **service_role key**: `eyJ...`

### 1.3 创建数据库表
1. 进入 SQL Editor
2. 执行 `database/schema.sql` 中的所有SQL代码
3. 执行 `database/security_policies.sql` 中的安全策略

### 1.4 设置Storage
1. 进入 Storage
2. 创建两个公开桶：
   - `reports` (存储Markdown文件)
   - `analysis` (存储HTML文件)

## 第二步：本地配置

### 2.1 更新环境变量
编辑 `.env` 文件：
```env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-anon-public-key
SUPABASE_SERVICE_KEY=your-service-role-key
FLASK_SECRET_KEY=your-production-secret-key
FLASK_ENV=production
DEBUG=False
```

### 2.2 初始化数据
```bash
python setup_supabase.py
```

### 2.3 上传示例文件
将以下文件上传到Supabase Storage：
- `uploads/reports/react_report.md` → `reports` 桶
- `uploads/analysis/react_analysis.html` → `analysis` 桶

## 第三步：部署到云平台

### 选项A：Vercel部署（推荐）

1. **准备代码**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Vercel部署**
   - 访问 https://vercel.com
   - 导入GitHub仓库
   - 配置环境变量（复制.env内容）
   - 部署

### 选项B：Railway部署

1. **推送代码到GitHub**
2. **Railway部署**
   - 访问 https://railway.app
   - 连接GitHub仓库
   - 配置环境变量
   - 部署

### 选项C：Render部署

1. **推送代码到GitHub**
2. **Render部署**
   - 访问 https://render.com
   - 创建新的Web Service
   - 连接GitHub仓库
   - 配置环境变量
   - 部署

## 第四步：验证部署

### 4.1 检查健康状态
访问 `https://your-app-url/health`

### 4.2 测试功能
1. 访问首页查看报告列表
2. 登录管理后台：`https://your-app-url/admin/login`
   - 邮箱: <EMAIL>
   - 密码: admin123
3. 测试报告创建和用户请求功能

## 第五步：生产环境优化

### 5.1 安全设置
- 更改默认管理员密码
- 设置强密码策略
- 启用HTTPS
- 配置CORS策略

### 5.2 性能优化
- 启用CDN
- 配置缓存
- 优化数据库查询
- 设置监控

### 5.3 备份策略
- 设置数据库自动备份
- 配置文件存储备份
- 制定灾难恢复计划

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查Supabase URL和密钥
   - 确认网络连接
   - 验证RLS策略

2. **文件上传失败**
   - 检查Storage桶权限
   - 验证文件大小限制
   - 确认文件类型允许

3. **部署失败**
   - 检查环境变量配置
   - 验证依赖包版本
   - 查看部署日志

### 联系支持
如果遇到问题，请：
1. 检查应用日志
2. 验证配置信息
3. 查看Supabase项目状态
4. 联系技术支持

---

🎉 **恭喜！您的项目研究报告平台已成功部署到Supabase！**
